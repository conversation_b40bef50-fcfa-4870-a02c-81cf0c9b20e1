'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'task_user_mapping',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                user_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                task_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'tasks',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
            },
            {
                underscored: true,
                paranoid: true,
            }
        )

        // Add a unique constraint to prevent duplicate mappings
        await queryInterface.addIndex('task_user_mapping', ['user_id', 'task_id'], {
            unique: true,
            name: 'unique_task_user_mapping',
            where: {
                deleted_at: null,
            },
        })
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('task_user_mapping')
    },
}
