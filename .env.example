# Database Configuration
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=fun2plan
DB_HOST=localhost
DB_PORT=5432

# JWT Configuration
JWT_ACCESS_SECRET=your_access_secret
JWT_REFRESH_SECRET=your_refresh_secret
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# API Configuration
API_VERSION=v1
PORT=3001
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM_NAME=Fun2Plan
EMAIL_FROM_ADDRESS=<EMAIL>

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
