import { ChatOpenAI } from '@langchain/openai'

async function model() {
    try {
        const config = {
            modelName: 'gpt-4o-mini',
            temperature: 0.3,
            maxTokens: 1500,
            verbose: false,
            openAIApiKey: process.env.OPENAI_API_KEY,
        }

        const chatModel = new ChatOpenAI(config)
        return chatModel
    } catch (err) {
        console.error('OpenAI connection failure:', err)
        return null
    }
}

export { model }
