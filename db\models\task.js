import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Task = sequelize.define(
    'tasks',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        feat_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'features',
                key: 'id',
            },
        },
        short_code: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'short_code cannot be null',
                },
                notEmpty: {
                    msg: 'short_code cannot be empty',
                },
            },
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'title cannot be null',
                },
                notEmpty: {
                    msg: 'title cannot be empty',
                },
            },
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        time_estimate_hrs: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        due_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        status_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        assignee: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id',
            },
        },
        department: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'departments',
                key: 'id',
            },
        },
        priority_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'priorities',
                key: 'id',
            },
        },
        kanban_position: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        bug: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
        },
    },
    {
        sequelize,
        modelName: 'Task',
        tableName: 'tasks',
        timestamps: true, // adds created_at and updated_at
        paranoid: true, // adds deleted_at (for soft deletes)
        underscored: true, // uses snake_case column names
    }
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const Feature = (await import('./feature.js')).default
        const Status = (await import('./status.js')).default
        const User = (await import('./user.js')).default
        const Department = (await import('./department.js')).default
        const Priority = (await import('./priority.js')).default
        const TaskUserMapping = (await import('./task-user-mapping.js')).default

        Task.belongsTo(Feature, { foreignKey: 'feat_id' })
        Task.belongsTo(Status, { foreignKey: 'status_id', as: 'taskStatus' })
        Task.belongsTo(Priority, { foreignKey: 'priority_id', as: 'taskPriority' })
        Task.belongsTo(User, { foreignKey: 'assignee', as: 'assignedUser' })
        Task.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })
        Task.belongsTo(Department, { foreignKey: 'department', as: 'taskDepartment' })
        Task.belongsToMany(Task, {
            through: 'task_dependency_mapping',
            as: 'dependentTasks',
            foreignKey: 'task_id',
            otherKey: 'dependant_task_id',
        })

        Task.belongsToMany(Task, {
            through: 'task_dependency_mapping',
            as: 'parentTasks',
            foreignKey: 'dependant_task_id',
            otherKey: 'task_id',
        })
        Task.belongsToMany(User, {
            foreignKey: 'task_id',
            as: 'taskAssignees',
            through: TaskUserMapping,
        })
    } catch (error) {
        console.error('Error setting up Task associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Task
