import { z } from 'zod'

// Schema for creating a new feature
export const createFeatureSchema = z.object({
    feat_code: z.string().min(1, 'Feature code is required'),
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    draft: z.boolean().optional().default(true),
    project_id: z.number().int().positive('Project ID must be a positive integer'),
    status_id: z.number().int().positive('Status ID must be a positive integer'),
})

// Schema for updating an existing feature
export const updateFeatureSchema = z
    .object({
        feat_code: z.string().min(1, 'Feature code is required').optional(),
        title: z.string().min(1, 'Title is required').optional(),
        description: z.string().optional(),
        draft: z.boolean().optional(),
        project_id: z.number().int().positive('Project ID must be a positive integer').optional(),
        status_id: z.number().int().positive('Status ID must be a positive integer').optional(),
    })
    .refine((data) => Object.keys(data).length > 0, {
        message: 'At least one field must be provided for update',
    })
