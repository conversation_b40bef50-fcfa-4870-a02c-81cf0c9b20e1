import { model } from '../../config/langchain.config.js'
import { io } from '../server.js'
async function featureDescIntrepretationModel() {
    try {
        io.on('connection', (socket) => {
            // Listen for a request event from the client
            console.log('WEBSOCKETCONNECTED')
            socket.on('request-feature-description', async (input) => {
                try {
                    const chatModel = await model()
                    if (!chatModel) {
                        throw new Error('Failed to initialize ChatOpenAI')
                    }
                    // const prompt = `${input} write an algorithm focused especially for this feature for creating this feature. The steps should be like commit messages so that it can be used as a task for project tracking. Strictly, the output should be concise and under 150 words. Prefix each task with 'BREAK', except the first. Provide only the steps without numbering. Ensure language is technical.Also give the time estimate for the task.Prefix each time estimate with 'TIME'. Ensure that all spellings are correct.`
                    const prompt = `${input}. Create an algorithm for implementing this feature. The output should be concise (under 150 words) and formatted as commit messages for project tracking. Prefix each step with 'BREAK' except the first one. Include time estimates prefixed with 'TIME'. Ensure technical language is used throughout. Correct spelling is mandatory.`

                    const response = await chatModel.stream(prompt)

                    for await (const chunk of response) {
                        try {
                            // console.log('--', chunk.content)
                            socket.emit('feature-response', chunk.content)
                        } catch (error) {
                            console.error('Error processing chunk:', error)
                            break
                        }
                    }

                    // Optionally, emit a success confirmation back to the client
                    socket.emit('success', 'Feature description generated successfully.')
                } catch (err) {
                    console.error('ERROR in feature interpretation model:', err)
                    // Optionally, emit an error event back to the client
                    socket.emit('error', 'An error occurred while generating the feature description.')
                }
            })
        })
    } catch (err) {
        console.error('ERROR in feature interpretation model:', err)
    }
}

export { featureDescIntrepretationModel }
