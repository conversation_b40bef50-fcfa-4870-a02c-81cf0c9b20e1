import { Op } from 'sequelize'
import Project from '../../db/models/project.js'
import User from '../../db/models/user.js'
import Workspace from '../../db/models/workspace.js'
import Status from '../../db/models/status.js'
import UserProjectMapping from '../../db/models/user-project-mapping.js'
import { AppError } from '../utils/app-error.js'
import { catchAsync } from '../utils/catch-async.js'
import Feature from '../../db/models/feature.js'
import Task from '../../db/models/task.js'
import { paginatedQuery, getPaginationParams } from '../utils/pagination.utils.js'
import Department from '../../db/models/department.js'
import { pool } from '../../config/database.js'
import { v4 as uuidv4 } from 'uuid'
import { OpenAI } from 'openai'
import { QdrantClient } from '@qdrant/js-client-rest'
import sequelize from '../../config/db.config.js'
import Priority from '../../db/models/priority.js'

// Initialize OpenAI
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
})

// Initialize Qdrant client
const qdrantClient = new QdrantClient({
    url: process.env.QDRANT_URL || 'http://localhost:6333',
})

// Constants for Qdrant collection
const COLLECTION_NAME = 'project_management_data'
const VECTOR_SIZE = 1536

// Function to generate embeddings using OpenAI
async function generateEmbedding(text) {
    try {
        const response = await openai.embeddings.create({
            model: 'text-embedding-3-small',
            input: text,
        })
        return response.data[0].embedding
    } catch (error) {
        console.error('Error generating embedding:', error)
        throw error
    }
}

// Function to ensure the collection exists
async function ensureCollectionExists() {
    try {
        await qdrantClient.getCollection(COLLECTION_NAME)
        console.log(`Collection '${COLLECTION_NAME}' exists`)
    } catch (error) {
        console.log(`Creating collection '${COLLECTION_NAME}'...`)
        await qdrantClient.createCollection(COLLECTION_NAME, {
            vectors: {
                size: VECTOR_SIZE,
                distance: 'Cosine',
            },
        })
        console.log(`Collection '${COLLECTION_NAME}' created successfully`)
    }
}

// Function to index project in Qdrant
async function indexProjectInQdrant(project, workspaceName) {
    try {
        const text = `Project: ${project.name}. Overview: ${project.overview || 'No overview provided'}. 
      Workspace: ${workspaceName || 'Unknown'}. 
      Status: ${'backlog'}.`

        const vector = await generateEmbedding(text)

        await qdrantClient.upsert(COLLECTION_NAME, {
            wait: true,
            points: [
                {
                    id: uuidv4(),
                    vector: vector,
                    payload: {
                        type: 'project',
                        id: project.id,
                        name: project.name,
                        overview: project.overview,
                        workspace_id: project.workspace_id,
                        workspace_name: workspaceName,
                        status: 'backlog',
                        text: text,
                    },
                },
            ],
        })

        console.log(`Project ${project.id} indexed in Qdrant`)
    } catch (error) {
        console.error('Error indexing project in Qdrant:', error)
        // Don't throw error to prevent main transaction from failing
    }
}

// Function to index feature in Qdrant
async function indexFeatureInQdrant(feature, projectName) {
    try {
        const text = `Feature ${feature.feat_code}: ${feature.title}. 
      Description: ${feature.description || 'No description provided'}. 
      Project: ${projectName || 'Unknown'}. 
      Status: ${'backlog'}. 
      Draft: ${feature.draft ? 'Yes' : 'No'}.`

        const vector = await generateEmbedding(text)

        await qdrantClient.upsert(COLLECTION_NAME, {
            wait: true,
            points: [
                {
                    id: uuidv4(),
                    vector: vector,
                    payload: {
                        type: 'feature',
                        id: feature.id,
                        feat_code: feature.feat_code,
                        title: feature.title,
                        description: feature.description,
                        project_id: feature.project_id,
                        project_name: projectName,
                        status: 'backlog',
                        draft: feature.draft,
                        text: text,
                    },
                },
            ],
        })

        console.log(`Feature ${feature.id} indexed in Qdrant`)
    } catch (error) {
        console.error('Error indexing feature in Qdrant:', error)
        // Don't throw error to prevent main transaction from failing
    }
}

// Function to index task in Qdrant
async function indexTaskInQdrant(task, featureCode, featureTitle, projectName) {
    try {
        const text = `Task ${task.short_code}: ${task.title}. 
      Description: ${task.description || 'No description provided'}. 
      Feature: ${featureCode || 'Unknown'} - ${featureTitle || 'Unknown'}. 
      Project: ${projectName || 'Unknown'}. 
      Status: ${'backlog'}. 
      Time estimate: ${task.time_estimate_hrs || 'Not estimated'} hours. 
      Assigned to: Unassigned.`

        const vector = await generateEmbedding(text)

        await qdrantClient.upsert(COLLECTION_NAME, {
            wait: true,
            points: [
                {
                    id: uuidv4(),
                    vector: vector,
                    payload: {
                        type: 'task',
                        id: task.id,
                        short_code: task.short_code,
                        title: task.title,
                        description: task.description,
                        feature_id: task.feat_id,
                        feature_code: featureCode,
                        feature_title: featureTitle,
                        project_name: projectName,
                        status: 'backlog',
                        time_estimate_hrs: task.time_estimate_hrs,
                        assignee: 'Unassigned',
                        text: text,
                    },
                },
            ],
        })

        console.log(`Task ${task.id} indexed in Qdrant`)
    } catch (error) {
        console.error('Error indexing task in Qdrant:', error)
        // Don't throw error to prevent main transaction from failing
    }
}

const createProject = catchAsync(async (req, res, next) => {
    const { name, overview, workspace_id, status_id, stack } = req.body
    const userId = req.user.id

    // Check if workspace exists and user has access to it
    const userWorkspace = await Workspace.findOne({
        where: { id: workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Check if status exists
    const status = await Status.findByPk(status_id)
    if (!status) {
        return next(new AppError('Status not found', 404))
    }
    const transaction = await sequelize.transaction()
    try {
        // Create the project
        const newProject = await Project.create(
            {
                name,
                overview,
                workspace_id,
                status_id,
                created_by: userId,
                stack,
            },
            { transaction }
        )

        await UserProjectMapping.create(
            {
                user_id: userId,
                project_id: newProject.id,
            },
            { transaction }
        )
        await transaction.commit()
        return res.status(201).json({
            status: 'success',
            data: newProject,
        })
    } catch (error) {
        await transaction.rollback()
        return next(new AppError(`Failed to create project: ${error.message}`, 500))
    }
})

const getAllProjects = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const { workspace_id, status_id, searchQuery } = req.query
    const paginationRequestData = getPaginationParams(req.query)
    // Build filter conditions
    const whereConditions = {}

    if (workspace_id) {
        whereConditions.workspace_id = workspace_id

        // Check if user has access to the workspace
        const userWorkspace = await Workspace.findOne({
            where: { id: workspace_id },
            include: [
                {
                    model: User,
                    where: { id: userId },
                    through: { attributes: [] },
                },
            ],
        })

        if (!userWorkspace) {
            return next(new AppError('Workspace not found or you do not have access to it', 404))
        }
    } else {
        // If no workspace_id is provided, get all projects from workspaces the user has access to
        const userWorkspaces = await Workspace.findAll({
            include: [
                {
                    model: User,
                    where: { id: userId },
                    through: { attributes: [] },
                },
            ],
            attributes: ['id'],
        })

        const workspaceIds = userWorkspaces.map((workspace) => workspace.id)
        whereConditions.workspace_id = { [Op.in]: workspaceIds }
    }

    if (status_id) {
        whereConditions.status_id = status_id
    }

    if (searchQuery) {
        whereConditions[Op.or] = [
            {
                name: {
                    [Op.iLike]: `%${searchQuery}%`, // Case-insensitive search
                },
            },
        ]
    }

    // Get completed status ID (assuming you have a status for completed tasks)
    const completedStatus = await Status.findOne({
        where: {
            short_code: 'completed', // Adjust this based on your status naming convention
        },
        attributes: ['id'],
    })

    const completedStatusId = completedStatus ? completedStatus.id : null

    // Get projects with associations and calculate progress
    const { data: projects, pagination } = await paginatedQuery(
        Project,
        {
            where: whereConditions,
            include: [
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'first_name', 'last_name'],
                },
                {
                    model: Workspace,
                    attributes: ['id', 'name'],
                },
                {
                    model: Status,
                    attributes: ['id', 'short_code', 'label', 'colour'],
                },
                {
                    model: Feature,
                    attributes: ['id', 'title', 'feat_code', 'status_id'],
                    include: [
                        {
                            model: Task,
                            attributes: ['id', 'status_id', 'updated_at', 'department', 'feat_id'],
                            include: [
                                {
                                    model: Department,
                                    as: 'taskDepartment',
                                    attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                                },
                            ],
                            separate: true, // Changed to false to prevent separate queries
                            order: [['updated_at', 'DESC']],
                        },
                    ],
                },
                {
                    model: User,
                    attributes: ['id', 'first_name', 'last_name', 'img_url'],
                    through: { attributes: [] },
                },
            ],
            order: [['created_at', 'DESC']],
        },
        paginationRequestData
    )

    // Calculate progress for each project
    const projectsWithProgress = projects.map((project) => {
        const projectObj = project.toJSON()

        // Calculate total tasks and completed tasks
        let totalTasks = 0
        let completedTasks = 0
        let totalFeatures = 0
        let completedFeatures = 0

        if (projectObj.features && projectObj.features.length > 0) {
            totalFeatures = projectObj.features.length

            // Get departments from 2 most recently updated tasks
            const recentDepartments = projectObj.features
                .flatMap((feature) => feature.tasks)
                .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
                .filter((task) => task && task.taskDepartment)
                .map((task) => JSON.stringify(task.taskDepartment))
                .filter((dept, index, self) => self.indexOf(dept) === index)
                .slice(0, 2)
                .map((dept) => JSON.parse(dept))

            projectObj.departments = recentDepartments

            // Rest of your counting logic...
            projectObj.features.forEach((feature) => {
                // Count completed features
                if (completedStatusId && feature.status_id === completedStatusId) {
                    completedFeatures++
                }

                // Count tasks within features
                if (feature.tasks && feature.tasks.length > 0) {
                    totalTasks += feature.tasks.length

                    // Count completed tasks
                    if (completedStatusId) {
                        feature.tasks.forEach((task) => {
                            if (task.status_id === completedStatusId) {
                                completedTasks++
                            }
                        })
                    }
                }
            })
        }

        const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

        projectObj.progress = {
            percentage: progress,
            completed: completedTasks,
            total: totalTasks,
        }

        projectObj.features = {
            completed: completedFeatures,
            total: totalFeatures,
        }
        projectObj.team = projectObj.users
        delete projectObj.users

        return projectObj
    })

    return res.status(200).json({
        status: 'success',
        results: projectsWithProgress.length,
        data: {
            projectData: projectsWithProgress,
            paginationData: pagination,
        },
    })
})

const getProjectById = catchAsync(async (req, res, next) => {
    const projectId = req.params.id
    const userId = req.user.id

    // Get the project with associations
    const project = await Project.findByPk(projectId, {
        include: [
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
            {
                model: Workspace,
                attributes: ['id', 'name'],
            },
            {
                model: Status,
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: Feature,
                attributes: ['id', 'title', 'feat_code', 'status_id'],
                include: [
                    {
                        model: Task,
                        attributes: ['id', 'status_id', 'updated_at', 'department', 'feat_id', 'time_estimate_hrs'],
                        include: [
                            {
                                model: Department,
                                as: 'taskDepartment',
                                attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                            },
                        ],
                        separate: true,
                        order: [['updated_at', 'DESC']],
                    },
                ],
            },
            {
                model: User,
                attributes: ['id', 'first_name', 'last_name', 'img_url'],
                through: { attributes: [] },
            },
        ],
    })

    if (!project) {
        return next(new AppError('Project not found', 404))
    }

    // Check if user has access to the workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: project.workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('You do not have access to this project', 403))
    }

    // Get completed status ID (assuming you have a status for completed tasks)
    const completedStatus = await Status.findOne({
        where: {
            short_code: 'completed', // Adjust this based on your status naming convention
        },
        attributes: ['id'],
    })

    const completedStatusId = completedStatus ? completedStatus.id : null

    // Calculate progress for the project
    const projectObj = project.toJSON()

    // Calculate total tasks and completed tasks
    let totalTasks = 0
    let completedTasks = 0
    let totalFeatures = 0
    let completedFeatures = 0
    let totalTimeEstimate = 0

    if (projectObj.features && projectObj.features.length > 0) {
        totalFeatures = projectObj.features.length

        // Get departments from 2 most recently updated tasks
        const recentDepartments = projectObj.features
            .flatMap((feature) => feature.tasks)
            .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
            .filter((task) => task && task.taskDepartment)
            .map((task) => JSON.stringify(task.taskDepartment))
            .filter((dept, index, self) => self.indexOf(dept) === index)
            .slice(0, 2)
            .map((dept) => JSON.parse(dept))

        projectObj.departments = recentDepartments

        // Count features and tasks
        projectObj.features.forEach((feature) => {
            // Count completed features
            if (completedStatusId && feature.status_id === completedStatusId) {
                completedFeatures++
            }

            // Count tasks within features
            if (feature.tasks && feature.tasks.length > 0) {
                totalTasks += feature.tasks.length

                // Count completed tasks
                if (completedStatusId) {
                    feature.tasks.forEach((task) => {
                        if (task.status_id === completedStatusId) {
                            completedTasks++
                        }
                        totalTimeEstimate += task.time_estimate_hrs || 0
                    })
                }
            }
        })
    }

    const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

    projectObj.progress = {
        percentage: progress,
        completed: completedTasks,
        total: totalTasks,
    }

    projectObj.features = {
        completed: completedFeatures,
        total: totalFeatures,
    }

    projectObj.estimated_effort = totalTimeEstimate
    projectObj.team = projectObj.users
    delete projectObj.users
    return res.status(200).json({
        status: 'success',
        data: projectObj,
    })
})

const updateProject = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const projectId = req.params.id
    const { name, overview, workspace_id, status_id } = req.body

    // Find the project
    const project = await Project.findByPk(projectId)
    if (!project) {
        return next(new AppError('Project not found', 404))
    }

    // Check if user has access to the current workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: project.workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('You do not have access to this project', 403))
    }

    // If workspace_id is being updated, check if user has access to the new workspace
    if (workspace_id && workspace_id !== project.workspace_id) {
        const newUserWorkspace = await Workspace.findOne({
            where: { id: workspace_id },
            include: [
                {
                    model: User,
                    where: { id: userId },
                    through: { attributes: [] },
                },
            ],
        })

        if (!newUserWorkspace) {
            return next(new AppError('Workspace not found or you do not have access to it', 404))
        }
    }

    // If status_id is being updated, check if status exists
    if (status_id && status_id !== project.status_id) {
        const status = await Status.findByPk(status_id)
        if (!status) {
            return next(new AppError('Status not found', 404))
        }
    }

    // Update the project
    await project.update({
        name: name !== undefined ? name : project.name,
        overview: overview !== undefined ? overview : project.overview,
        workspace_id: workspace_id !== undefined ? workspace_id : project.workspace_id,
        status_id: status_id !== undefined ? status_id : project.status_id,
    })

    return res.status(200).json({
        status: 'success',
        data: project,
    })
})

const deleteProject = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const projectId = req.params.id

    // Find the project
    const project = await Project.findByPk(projectId)
    if (!project) {
        return next(new AppError('Project not found', 404))
    }

    // Check if user has access to the workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: project.workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('You do not have access to this project', 403))
    }

    // Soft delete the project (using paranoid option)
    await project.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})

/**
 * Add a user to a project
 */
const addUserToProject = catchAsync(async (req, res, next) => {
    const { project_id, user_id } = req.body
    const currentUserId = req.user.id

    // Check if the current user has access to this project's workspace
    const project = await Project.findByPk(project_id, {
        include: [
            {
                model: Workspace,
                include: [
                    {
                        model: User,
                        where: { id: currentUserId },
                        through: { attributes: [] },
                    },
                ],
            },
        ],
    })

    if (!project) {
        return next(new AppError('Project not found or you do not have access to it', 404))
    }

    // Check if the user to be added exists
    const userToAdd = await User.findByPk(user_id)
    if (!userToAdd) {
        return next(new AppError('User not found', 404))
    }

    // Check if the user is already a member of the project
    const existingMapping = await UserProjectMapping.findOne({
        where: {
            user_id,
            project_id,
        },
    })

    if (existingMapping) {
        return next(new AppError('User is already a member of this project', 400))
    }

    // Create the mapping
    const newMapping = await UserProjectMapping.create({
        user_id,
        project_id,
    })

    return res.status(201).json({
        status: 'success',
        data: newMapping,
    })
})

/**
 * Remove a user from a project
 */
const removeUserFromProject = catchAsync(async (req, res, next) => {
    const { project_id, user_id } = req.params
    const currentUserId = req.user.id

    // Check if the current user has access to this project's workspace
    const project = await Project.findByPk(project_id, {
        include: [
            {
                model: Workspace,
                include: [
                    {
                        model: User,
                        where: { id: currentUserId },
                        through: { attributes: [] },
                    },
                ],
            },
        ],
    })

    if (!project) {
        return next(new AppError('Project not found or you do not have access to it', 404))
    }

    // Find the mapping to remove
    const mappingToRemove = await UserProjectMapping.findOne({
        where: {
            user_id,
            project_id,
        },
    })

    if (!mappingToRemove) {
        return next(new AppError('User is not a member of this project', 404))
    }

    // Don't allow removing the project creator
    if (project.created_by === parseInt(user_id)) {
        return next(new AppError('Cannot remove the project creator from the project', 403))
    }

    // Remove the mapping (soft delete)
    await mappingToRemove.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})

/**
 * Save a project with its features and tasks to the database and index in Qdrant
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next middleware function
 * @returns {Promise<void>} - JSON response
 */
const saveProject = catchAsync(async (req, res, next) => {
    const client = await pool.connect()
    // Start transaction
    const transaction = await sequelize.transaction()
    try {
        // Ensure Qdrant collection exists
        await ensureCollectionExists()

        // Extract data from request body
        const { userId, projectName, projectDescription, workspaceId, tasks, techStack, existingProjectId } = req.body
        console.log(existingProjectId, userId)
        // Validate inputs
        if (!workspaceId || !Array.isArray(tasks) || tasks.length === 0) {
            return next(new AppError('Missing required fields: project name, workspace, and at least one task are required', 400))
        }

        // Check if user has access to the workspace
        const userWorkspace = await Workspace.findOne({
            where: { id: workspaceId },
            include: [
                {
                    model: User,
                    where: { id: userId },
                    through: { attributes: [] },
                },
            ],
        })

        if (!userWorkspace) {
            return next(new AppError('Workspace not found or you do not have access to it', 404))
        }

        // Get default status ID - backlog
        const statusResult = await Status.findOne({
            where: { short_code: 'backlog' },
            attributes: ['id'],
        })

        // const statusId = statusResult.rows[0]?.id
        const statusId = statusResult.id

        if (!statusId) {
            return next(new AppError('Status configuration error: could not find pending status', 500))
        }

        let projectId,
            featureId,
            projectName_final,
            featureCode,
            isNewProject = false

        // Check if we're updating an existing project or creating a new one
        if (existingProjectId) {
            // Validate that the project exists and user has access
            const existingProjectResult = await Project.findByPk(existingProjectId, {
                include: [
                    {
                        model: User,
                        where: { id: userId },
                        through: { attributes: [] },
                    },
                ],
            })

            if (!existingProjectResult) {
                return next(new AppError('Project not found or you do not have access to it', 404))
            }

            projectId = existingProjectId
            projectName_final = existingProjectResult.name

            // Create a new feature for the existing project
            const featureCode = `F-${uuidv4().substring(0, 8).toUpperCase()}`

            const featureResult = await Feature.create(
                {
                    feat_code: featureCode,
                    title: projectName || 'New Feature',
                    description: projectDescription || '',
                    draft: false,
                    project_id: projectId,
                    status_id: statusId,
                    created_by: userId,
                },
                { transaction }
            )

            featureId = featureResult.id
        } else {
            // Create new project
            isNewProject = true
            projectName_final = projectName

            const projectResult = await Project.create(
                {
                    name: projectName,
                    overview: projectDescription || '',
                    workspace_id: workspaceId,
                    status_id: statusId,
                    stack: techStack || {},
                    created_by: userId,
                },
                { transaction }
            )

            projectId = projectResult.rows[0].id

            // Map user to project
            await UserProjectMapping.create(
                {
                    user_id: userId,
                    project_id: projectId,
                },
                { transaction }
            )

            // Create feature with unique code
            const featureCode = `F-${uuidv4().substring(0, 8).toUpperCase()}`

            const featureResult = await Feature.create(
                {
                    feat_code: featureCode,
                    title: 'New Feature: Chat Integration',
                    description: projectDescription || '',
                    draft: false,
                    project_id: projectId,
                    status_id: statusId,
                    created_by: userId,
                },
                { transaction }
            )

            featureId = featureResult.rows[0].id
        }

        // Get cached mappings for departments and priorities
        const priorityMap = await getPriorityMapping(client)

        // Get existing tasks in the backlog status for this feature
        const existingTasks = await Task.findAll({
            include: [
                {
                    model: Feature,
                    attributes: ['id', 'project_id'],
                    include: [
                        {
                            model: Project,
                            attributes: ['id', 'workspace_id'],
                            where: {
                                workspace_id: workspaceId,
                            },
                            include: [
                                {
                                    model: User,
                                    where: { id: userId },
                                    through: { attributes: [] },
                                    required: true,
                                },
                            ],
                        },
                    ],
                    required: true,
                },
            ],
            order: [['kanban_position', 'ASC']],
            transaction,
        })

        // Count valid tasks (those with titles)
        const validTaskCount = tasks.filter((task) => task.title).length

        // Shift existing tasks down to make room at the top
        if (existingTasks.length > 0 && validTaskCount > 0) {
            for (const existingTask of existingTasks) {
                await existingTask.update({ kanban_position: existingTask.kanban_position + validTaskCount }, { transaction })
            }
        }

        // Insert tasks at the top of the column
        const taskIds = []
        let position = 0

        for (const task of tasks) {
            if (!task.title) {
                continue // Skip tasks without titles
            }

            // Generate unique task code
            const shortCode = `T-${uuidv4().substring(0, 8).toUpperCase()}`

            // Determine department ID from role
            let departmentId
            if (task.category) {
                const result = await Department.findOne({
                    where: { short_code: task.category.toLowerCase() },
                    attributes: ['id'],
                })
                departmentId = result ? result.id : null
            }

            // Determine priority ID
            const priorityId = priorityMap[task.priority?.toLowerCase()] || priorityMap['medium']
            const estimatedHours = parseFloat(task.estimatedHours) || 0
            const now = new Date()
            const dueDate = estimatedHours > 0 ? new Date(now.getTime() + estimatedHours * 60 * 60 * 1000) : null

            const taskResult = await Task.create(
                {
                    feat_id: featureId,
                    short_code: shortCode,
                    title: task.title,
                    description: task.description || '',
                    time_estimate_hrs: estimatedHours,
                    due_date: dueDate,
                    status_id: statusId,
                    department: departmentId,
                    priority_id: priorityId,
                    created_by: userId,
                    kanban_position: position++, // Position at the top of the column
                    assignee: userId,
                },
                { transaction }
            )

            taskIds.push({
                id: taskResult.id,
                shortCode,
                title: task.title,
                description: task.description || '',
                timeEstimateHrs: estimatedHours,
            })
        }

        // Commit transaction
        await transaction.commit()

        // Get workspace name for embedding
        const workspaceName = userWorkspace.name

        // Only index new projects in Qdrant
        if (isNewProject) {
            const projectData = {
                id: projectId,
                name: projectName_final,
                overview: projectDescription || '',
                workspace_id: workspaceId,
            }

            indexProjectInQdrant(projectData, workspaceName).catch((err) => {
                console.error('Failed to index project in Qdrant:', err)
            })
        }

        // Index feature in Qdrant (non-blocking)
        const featureData = {
            id: featureId,
            feat_code: featureCode,
            title: projectName || 'New Feature',
            description: projectDescription || '',
            project_id: projectId,
            draft: false,
        }

        indexFeatureInQdrant(featureData, projectName_final).catch((err) => {
            console.error('Failed to index feature in Qdrant:', err)
        })

        // Index tasks in Qdrant (non-blocking)
        for (const task of taskIds) {
            const taskData = {
                id: task.id,
                short_code: task.shortCode,
                title: task.title,
                description: task.description,
                time_estimate_hrs: task.timeEstimateHrs,
                feat_id: featureId,
            }

            indexTaskInQdrant(taskData, featureCode, projectName_final, projectName_final).catch((err) => {
                console.error('Failed to index task in Qdrant:', err)
            })
        }

        return res.status(201).json({
            status: 'success',
            message: isNewProject ? 'Project created successfully' : 'Tasks added to existing project successfully',
            data: {
                project: {
                    id: projectId,
                    name: projectName_final,
                    workspace_id: workspaceId,
                    isNew: isNewProject,
                },
                feature: {
                    id: featureId,
                    code: featureCode,
                },
                taskCount: taskIds.length,
            },
        })
    } catch (error) {
        // Rollback transaction on error
        await transaction.rollback()

        console.error('Error saving project:', error)
        return next(new AppError(`Failed to save project: ${error.message}`, 500))
    } finally {
        client.release()
    }
})
const taskDistributionByDepartment = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const { workspace_id, project_id } = req.query

    if (!workspace_id) {
        return next(new AppError('Workspace ID is required', 400))
    }

    if (!project_id) {
        return next(new AppError('Project ID is required', 400))
    }

    const userWorkspace = await Workspace.findOne({
        where: { id: workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    const completedStatus = await Status.findOne({
        where: { short_code: 'completed' },
        attributes: ['id'],
    })

    const completedStatusId = completedStatus?.id || null

    const now = new Date()
    const sixMonthsAgo = new Date(now)
    sixMonthsAgo.setMonth(now.getMonth() - 6)

    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)

    const projects = await Project.findAll({
        where: { id: project_id },
        include: [
            { model: Workspace, attributes: ['id', 'name'] },
            { model: User, as: 'creator', attributes: ['id', 'first_name', 'last_name'] },
            {
                model: Feature,
                attributes: ['id', 'title', 'feat_code'],
                include: [
                    {
                        model: Task,
                        attributes: ['id', 'status_id', 'updated_at'],
                        include: [
                            {
                                model: Department,
                                as: 'taskDepartment',
                                attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                            },
                        ],
                        separate: true,
                        where: { created_at: { [Op.gte]: sixMonthsAgo } },
                        order: [['updated_at', 'DESC']],
                    },
                ],
            },
            {
                model: User,
                attributes: ['id', 'first_name', 'last_name', 'img_url'],
                through: { attributes: [] },
            },
        ],
        order: [['created_at', 'DESC']],
    })

    const departmentMap = new Map()
    let overallStats = {
        current_month: { total: 0, completed: 0 },
        previous_month: { total: 0, completed: 0 },
        trend: 0,
    }

    for (const project of projects) {
        for (const feature of project.features || []) {
            for (const tsk of feature.tasks || []) {
                const task = tsk.toJSON()
                const dept = task.taskDepartment
                if (!dept || !dept.id) continue

                const deptId = dept.id
                if (!departmentMap.has(deptId)) {
                    departmentMap.set(deptId, {
                        department: {
                            id: deptId,
                            short_code: dept.short_code,
                            label: dept.label,
                            colour: {
                                text: dept.colour?.text || '#000000',
                                border: dept.colour?.border || '#0000001F',
                                background: dept.colour?.background || '#0000000D',
                            },
                        },
                        total_tasks: 0,
                        completed_tasks: 0,
                        current_month: { total: 0, completed: 0 },
                        previous_month: { total: 0, completed: 0 },
                        completion_percentage: 0,
                        trend: 0,
                    })
                }

                const stats = departmentMap.get(deptId)
                stats.total_tasks += 1
                if (task.status_id === completedStatusId) stats.completed_tasks += 1

                const updatedAt = new Date(task.updated_at)

                if (updatedAt >= currentMonthStart) {
                    stats.current_month.total += 1
                    if (task.status_id === completedStatusId) stats.current_month.completed += 1
                    overallStats.current_month.total += 1
                    if (task.status_id === completedStatusId) overallStats.current_month.completed += 1
                } else if (updatedAt >= previousMonthStart && updatedAt <= previousMonthEnd) {
                    stats.previous_month.total += 1
                    if (task.status_id === completedStatusId) stats.previous_month.completed += 1
                    overallStats.previous_month.total += 1
                    if (task.status_id === completedStatusId) overallStats.previous_month.completed += 1
                }
            }
        }
    }

    // Calculate overall trend
    const curPercent =
        overallStats.current_month.total > 0
            ? Math.round((overallStats.current_month.completed / overallStats.current_month.total) * 100)
            : 0

    const prevPercent =
        overallStats.previous_month.total > 0
            ? Math.round((overallStats.previous_month.completed / overallStats.previous_month.total) * 100)
            : 0

    overallStats.trend = curPercent - prevPercent
    let trendMessage = 'No change in trend this month'
    if (overallStats.trend > 0) {
        trendMessage = `Trending up by ${overallStats.trend}% in task completeion this month`
    } else if (overallStats.trend < 0) {
        trendMessage = `Trending down by ${Math.abs(overallStats.trend)}% in task completion this month`
    }

    const result = Array.from(departmentMap.values()).map((dept) => {
        dept.completion_percentage = dept.total_tasks > 0 ? Math.round((dept.completed_tasks / dept.total_tasks) * 100) : 0

        const currentMonthPercent =
            dept.current_month.total > 0 ? Math.round((dept.current_month.completed / dept.current_month.total) * 100) : 0

        const previousMonthPercent =
            dept.previous_month.total > 0 ? Math.round((dept.previous_month.completed / dept.previous_month.total) * 100) : 0

        dept.trend = currentMonthPercent - previousMonthPercent

        return {
            ...dept.department,
            total_tasks_count: dept.total_tasks,
            completed_tasks_count: dept.completed_tasks,
            completion_percentage: dept.completion_percentage,
            trend: dept.trend,
            trend_message: trendMessage,
            calculated_date_from: sixMonthsAgo.toISOString(),
            calculated_date_to: now.toISOString(),
        }
    })

    let topCompletion = Math.max(...result.map((dept) => dept.completion_percentage))

    result.forEach((dept) => {
        dept.is_top_department = dept.completion_percentage >= topCompletion
    })

    return res.status(200).json({
        status: 'success',
        data: result,
    })
})

/**
 * Helper function to retrieve department mapping from database
 */
async function getDepartmentMapping() {
    const departmentResult = await client.query('SELECT id, short_code, label FROM departments')
    return departmentResult.rows.reduce((acc, curr) => {
        acc[curr.short_code.toLowerCase()] = curr.id
        acc[curr.label.toLowerCase()] = curr.id
        return acc
    }, {})
}

/**
 * Helper function to retrieve priority mapping from database
 */
async function getPriorityMapping() {
    const priorityResult = await Priority.findAll({
        attributes: ['id', 'label'],
    })
    return priorityResult.reduce((acc, curr) => {
        acc[curr.label.toLowerCase()] = curr.id
        return acc
    }, {})
}

/**
 * Maps a role description to a department ID ==================== REMOVE IF NOT REEQUIRED
 */
function mapRoleToDepartment(role, departmentMap) {
    if (!role) return null

    const roleLower = role.toLowerCase()

    // Direct match with department names
    if (departmentMap[roleLower]) {
        return departmentMap[roleLower]
    }

    // Fuzzy matching for common roles
    if (roleLower.includes('front')) return departmentMap['frontend']
    if (roleLower.includes('back')) return departmentMap['backend']
    if (roleLower.includes('devops')) return departmentMap['devops']
    if (roleLower.includes('qa') || roleLower.includes('test')) return departmentMap['qa']
    if (roleLower.includes('design') || roleLower.includes('ui') || roleLower.includes('ux')) return departmentMap['design']

    // Default to null if no match
    return null
}

export {
    createProject,
    getAllProjects,
    getProjectById,
    updateProject,
    deleteProject,
    addUserToProject,
    removeUserFromProject,
    saveProject,
    taskDistributionByDepartment,
}
