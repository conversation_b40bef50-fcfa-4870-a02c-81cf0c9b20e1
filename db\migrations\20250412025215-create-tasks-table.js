'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'tasks',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                feat_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'features',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                short_code: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                title: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                description: {
                    type: Sequelize.TEXT,
                    allowNull: true,
                },
                time_estimate_hrs: {
                    type: Sequelize.FLOAT,
                    allowNull: true,
                },
                start_date: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
                due_date: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
                status_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'statuses',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                assignee: {
                    type: Sequelize.INTEGER,
                    allowNull: true,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                department: {
                    type: Sequelize.INTEGER,
                    allowNull: true,
                    references: {
                        model: 'departments',
                        key: 'id',
                    },
                },
                priority_id: {
                    type: Sequelize.INTEGER,
                    allowNull: true,
                    references: {
                        model: 'priorities',
                        key: 'id',
                    },
                },
                kanban_position: {
                    type: Sequelize.INTEGER,
                    allowNull: true,
                },
                bug: {
                    type: Sequelize.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                created_by: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                },
            },
            {
                underscored: true,
                paranoid: true,
                timestamps: true,
            }
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('tasks')
    },
}
