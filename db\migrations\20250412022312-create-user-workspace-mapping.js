'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'user_workspace_mapping',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                user_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                workspace_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'workspaces',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
            },
            {
                underscored: true,
                paranoid: true,
            }
        )

        // Add a unique constraint to prevent duplicate mappings
        await queryInterface.addIndex('user_workspace_mapping', ['user_id', 'workspace_id'], {
            unique: true,
            name: 'unique_user_workspace_mapping',
            where: {
                deleted_at: null,
            },
        })
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('user_workspace_mapping')
    },
}
