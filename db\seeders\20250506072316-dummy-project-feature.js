'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert('features', [
            {
                feat_code: 'FEAT001',
                title: 'User Authentication',
                description: 'Implement user authentication using JWT.',
                draft: true,
                project_id: 1, // Assuming the project with ID 1 exists
                status_id: 1, // Assuming the status with ID 1 exists
                created_by: 1, // Assuming the user with ID 1 exists
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                feat_code: 'FEAT002',
                title: 'Dashboard UI',
                description: 'Create the UI for the user dashboard.',
                draft: false,
                project_id: 1,
                status_id: 1,
                created_by: 1,
                created_at: new Date(),
                updated_at: new Date(),
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('features', null, {})
    },
}
