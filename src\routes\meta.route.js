import { Router } from 'express'
import { authentication } from '../controller/auth.controller.js'
import {
    getAssignees,
    getDepartments,
    getFeatures,
    getPriorities,
    getProjects,
    getRoles,
    getStatuses,
    getTimezones,
    getUserTypes,
} from '../controller/meta.controller.js'

const metaRouter = Router()

metaRouter.route('/roles').get(getRoles)
metaRouter.route('/timezones').get(getTimezones)

metaRouter.use(authentication)

metaRouter.route('/departments').get(getDepartments)
metaRouter.route('/statuses').get(getStatuses)
metaRouter.route('/priorities').get(getPriorities)
metaRouter.route('/projects').get(getProjects)
metaRouter.route('/features').get(getFeatures)
metaRouter.route('/assignees').get(getAssignees)
metaRouter.route('/user-types').get(getUserTypes)

export default metaRouter
