'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert(
            'departments',
            [
                {
                    short_code: 'design',
                    label: 'Design',
                    colour: JSON.stringify({
                        background: '#007BFF0D',
                        text: '#007BFF',
                        border: '#007BFF1F',
                    }),
                    icon: 'PaintBrushIcon',
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    short_code: 'development',
                    label: 'Development',
                    colour: JSON.stringify({
                        background: '#28A74514',
                        text: '#28A745',
                        border: '#28A7451F',
                    }),
                    icon: 'CodeBracketIcon',
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    short_code: 'qa',
                    label: 'QA',
                    colour: JSON.stringify({
                        background: '#F4EDF7',
                        text: '#6E65AA',
                        border: '#6E65AA1F',
                    }),
                    icon: 'BugAntIcon',
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    short_code: 'devops',
                    label: 'DevOps',
                    colour: JSON.stringify({
                        background: '#FED7AA1F',
                        text: '#F97316',
                        border: '#FED7AA1F',
                    }),
                    icon: 'ServerStackIcon',
                    created_at: new Date(),
                    updated_at: new Date(),
                },
            ],
            {},
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('departments', null, {})
    },
}
