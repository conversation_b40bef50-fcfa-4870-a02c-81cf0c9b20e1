// API middleware for common operations

export const apiLogger = (req, res, next) => {
  const startTime = new Date()
  process.stdout.write(`${startTime.toISOString()} - ${req.method} ${req.originalUrl}`)

  res.on('finish', () => {
    const duration = new Date() - startTime;
    console.log(` - Status: ${res.statusCode} - Duration: ${duration}ms`);
  });

  next()
}

// You can add rate limiting middleware here if needed
// Example with express-rate-limit (you would need to install this package):
/*
import { rateLimit } from 'express-rate-limit'

export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per window
  standardHeaders: true,
  legacyHeaders: false
})
*/
