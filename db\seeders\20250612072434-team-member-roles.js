'use strict'

module.exports = {
    async up(queryInterface, Sequelize) {
        const roles = [
            'Founder',
            'CEO',
            'Project Manager',
            'DevOps Engineer',
            'Software Tester',
            'Frontend Developer',
            'Backend Developer',
            'Fullstack Developer',
            'Designer',
        ]

        await queryInterface.bulkInsert(
            'team_member_roles',
            roles.map((role) => ({
                role,
                createdAt: new Date(),
                updatedAt: new Date(),
            })),
            {}
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('team_member_roles', null, {})
    },
}
