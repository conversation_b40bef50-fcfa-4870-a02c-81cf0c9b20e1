import { Router } from 'express'
import authRouter from './auth.route.js'
import userRouter from './user.route.js'
import projectRouter from './project.route.js'
import llmRouter from './llm.route.js'
import featureRouter from './feature.route.js'
import taskRouter from './task.route.js'
import workspaceRouter from './workspace.route.js'
import plannerRouter from './planner.route.js'
import metaRouter from './meta.route.js'

const router = Router()

const API_VERSION = 'v1'
const API_BASE = process.env.NODE_ENV == 'production' ? `/${API_VERSION}` : `/api/${API_VERSION}`

const routes = [
    { path: '/auth', router: authRouter },
    { path: '/user', router: userRouter },
    { path: '/project', router: projectRouter },
    { path: '/chat', router: llmRouter },
    { path: '/feature', router: featureRouter },
    { path: '/task', router: taskRouter },
    { path: '/workspace', router: workspaceRouter },
    { path: '/planner', router: plannerRouter },
    { path: '/meta', router: metaRouter },
    // Add new routes here as they are created
]

// Mount all routes
routes.forEach((route) => {
    router.use(`${API_BASE}${route.path}`, route.router)
})

export default router
