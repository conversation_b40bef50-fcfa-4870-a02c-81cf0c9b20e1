'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert('projects', [
            {
                name: 'Project Alpha',
                overview: 'Initial project for testing and onboarding',
                workspace_id: 1,
                status_id: 1,
                stack: JSON.stringify(['React', 'Node.js', 'PostgreSQL']),
                created_by: 1,
                created_at: new Date(),
                updated_at: new Date(),
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('projects', null, {})
    },
}
