import { ChatOpenAI } from '@langchain/openai'
import { ChatPromptTemplate } from '@langchain/core/prompts'
import { StringOutputParser } from '@langchain/core/output_parsers'

const sendEvent = (res, event, data) => {
    res.write(`event: ${event}\n`)
    res.write(`data: ${JSON.stringify(data)}\n\n`)
}

// Store active conversation sessions
const conversationSessions = new Map()

// Conversation prompt template
const conversationSystemTemplate = `# AI Project Planner Assistant

You're a friendly project planning assistant that helps turn ideas into clear requirements. Keep it conversational and helpful.

**Your job:** Get the key details needed to create actionable tasks for developers.

## What you need to find out:
- **What are we building?** (project name, main purpose, who it's for)
- **What should it do?** (key features, must-haves vs nice-to-haves)
- **How should we build it?** (preferred technologies, existing systems to work with)

## How to interact:
- Start with a warm greeting and ask about their project idea
- Ask 1-2 focused questions at a time (not a long list!)
- If they're unsure, offer helpful suggestions
- Keep the conversation flowing naturally
- Fill in reasonable assumptions for missing details

## When you have enough info, summarize like this:

**Project:** [Name and clear description]
**Requirements:** [Key features and what success looks like]  
**Tech Stack:** [Technologies and tools to use]

Then say: "Ready to break this down into tasks? Just type 'generate tasks' and I'll create your implementation plan!"

**Remember:** Be helpful, not overwhelming. Make this feel like chatting with a knowledgeable friend, not an interrogation.`
// const conversationSystemTemplate = `You are a project planning expert helping a product manager define requirements for task generation. Your goal is to gather comprehensive details that will enable the creation of specific, actionable tasks for developers.

// Guide the conversation to gather ALL the following details:

// 1. Feature/Project: Get a clear name and detailed description that explains the purpose, scope, and business value.
// 2. Requirements: Collect specific functional and non-functional requirements, user stories, acceptance criteria, edge cases, and constraints.
// 3. Tech Stack: Identify all technologies, frameworks, libraries, and tools that will be used or needed.

// Start by introducing yourself and asking about the feature/project. Ask focused follow-up questions to gather missing information. If the product manager is unsure about details:
// - Suggest reasonable options based on industry standards
// - Make practical recommendations based on the information provided
// - Help them think through implications of different choices

// After each response, assess what information is still missing and focus your next question on the highest priority gap. Be conversational but direct. Aim to complete the conversation in 5-10 messages.

// For effective task breakdown, try to uncover:
// - Clear boundaries of the project scope
// - Specific user interactions and flows
// - Data structures and relationships
// - Integration points with other systems
// - Performance or scalability requirements
// - Security considerations
// - Testing approaches
// - Deployment expectations

// Once you have gathered sufficient information, summarize all collected details in this EXACT format:
// - Feature/Project: [name and comprehensive description]
// - Requirements: [detailed list of requirements and acceptance criteria]
// - Tech Stack: [comprehensive list of technologies to use]

// End with: "Based on this information, I'll now generate a comprehensive task list for implementation. Type '2' or 'Generate tasks' when you're ready to create the task list."`

/**
 * Extract information from conversation summary
 * @param {string} summary - Conversation summary
 * @returns {Object} Extracted project information
 */
const extractInfoFromSummary = (summary) => {
    try {
        let feature = ''
        let requirement = ''
        let tech_stack = ''
        const lines = summary.split('\n')
        let currentSection = ''

        for (const line of lines) {
            if (line.startsWith('- Feature/Project:')) {
                currentSection = 'feature'
                feature = line.replace('- Feature/Project:', '').trim()
            } else if (line.startsWith('- Requirements:')) {
                currentSection = 'requirement'
                requirement = line.replace('- Requirements:', '').trim()
            } else if (line.startsWith('- Tech Stack:')) {
                currentSection = 'tech_stack'
                tech_stack = line.replace('- Tech Stack:', '').trim()
            } else if (line.trim() !== '') {
                switch (currentSection) {
                    case 'feature':
                        feature += ' ' + line.trim()
                        break
                    case 'requirement':
                        requirement += ' ' + line.trim()
                        break
                    case 'tech_stack':
                        tech_stack += ' ' + line.trim()
                        break
                }
            }
        }

        if (!feature) feature = 'Not specified'
        if (!requirement) requirement = 'Not specified'
        if (!tech_stack) tech_stack = 'Not specified'

        return { feature, requirement, tech_stack }
    } catch (error) {
        console.error('Error extracting information from summary:', error)

        // Attempt to extract information with a more forgiving approach
        const featureMatch = summary.match(/Feature\/Project:([^\n]*)/i)
        const requirementMatch = summary.match(/Requirements:([^\n]*)/i)
        const techStackMatch = summary.match(/Tech Stack:([^\n]*)/i)

        return {
            feature: featureMatch ? featureMatch[1].trim() : 'Not specified',
            requirement: requirementMatch ? requirementMatch[1].trim() : 'Not specified',
            tech_stack: techStackMatch ? techStackMatch[1].trim() : 'Not specified',
        }
    }
}

/**
 * Extract summary from conversation
 * @param {Object} conversationResult - Conversation result object
 * @returns {string} Summary text
 */
const extractSummaryFromConversation = (conversationResult) => {
    const messages = conversationResult.messages || []

    for (let i = messages.length - 1; i >= 0; i--) {
        if (messages[i].type === 'ai') {
            return messages[i].content
        }
    }

    return 'No summary found'
}

export const continueConversation = async (message, sessionId, res) => {
    try {
        // Get or create conversation history
        if (!conversationSessions.has(sessionId)) {
            conversationSessions.set(sessionId, {
                messages: [],
            })
        }

        const session = conversationSessions.get(sessionId)

        // Add user message to session
        session.messages.push({
            type: 'human',
            content: message,
        })

        // Create properly formatted message history
        const messageHistory = []

        // Add system message first
        messageHistory.push(['system', conversationSystemTemplate])

        // Add all previous messages in the correct order
        for (const msg of session.messages) {
            // Skip the current message as we'll add it separately at the end
            if (msg.type === 'human' && msg.content === message) continue

            if (msg.type === 'human') {
                messageHistory.push(['human', msg.content])
            } else if (msg.type === 'ai') {
                // LangChain uses 'assistant' for AI messages
                messageHistory.push(['assistant', msg.content])
            }
        }

        // Add the current message directly (not using template)
        messageHistory.push(['human', message])

        // Create the chat prompt template
        const chatPrompt = ChatPromptTemplate.fromMessages(messageHistory)

        // Initialize the LLM with streaming enabled
        const model = new ChatOpenAI({
            modelName: 'gpt-4o-mini',
            temperature: 0.3,
            streaming: true,
        })

        // Create the chain
        const chain = chatPrompt.pipe(model).pipe(new StringOutputParser())

        // Stream the response
        const stream = await chain.stream({})

        // Process each chunk and send as SSE
        let responseContent = ''

        // Properly format SSE events
        const sendEvent = (event, data) => {
            res.write(`event: ${event}\n`)
            res.write(`data: ${JSON.stringify(data)}\n\n`)
        }

        for await (const chunk of stream) {
            // IMPORTANT: Send only the new chunk, not the accumulated content
            // This is crucial to avoid duplication in the frontend
            sendEvent('chunk', { content: chunk })
            responseContent += chunk
        }

        // Send completion event
        sendEvent('complete', { message: 'Stream completed' })

        // Add AI response to session AFTER the complete response is received
        session.messages.push({
            type: 'ai',
            content: responseContent,
        })

        // End the response
        res.end()
    } catch (error) {
        console.error('Error in conversation:', error)

        // Send error message if streaming fails
        res.write(`event: error\ndata: ${JSON.stringify({ error: 'Error generating response' })}\n\n`)
        res.end()
        throw error
    }
}

/**
 * Generate tasks based on conversation history
 * @param {string} sessionId - Conversation session ID
 * @param {Response} res - Express response object for streaming
 * @returns {Promise<void>} - Streams response directly to client
 */
export const generateAITasks = async (sessionId, res) => {
    try {
        // Set headers for SSE
        res.setHeader('Content-Type', 'text/event-stream')
        res.setHeader('Cache-Control', 'no-cache')
        res.setHeader('Connection', 'keep-alive')
        res.setHeader('X-Accel-Buffering', 'no') // For Nginx proxy

        // Enable CORS if needed
        res.setHeader('Access-Control-Allow-Origin', '*')
        res.setHeader('Access-Control-Allow-Headers', '*')

        const session = conversationSessions.get(sessionId)

        if (!session) {
            sendEvent(res, 'error', { error: 'No active planning session found. Please start a conversation first.' })
            return res.end()
        }

        // Extract summary from conversation
        const summary = extractSummaryFromConversation({ messages: session.messages })
        const extractedInfo = extractInfoFromSummary(summary)

        // Send initial connected message
        sendEvent(res, 'message', { status: 'connected' })

        // Create the task generation prompt with streaming format
        const systemPrompt = `You are a project manager at a software development firm.
        Given a project summary and extracted information, break it down into clear, user stories for web app development.
        The task description should be instructive including libraries and support material which helps the development and devops. The description should be very elaborate like a user story and in atleast 200 words in markdown .
        
        Output Format Instructions:
        - Each task must be a separate JSON object on its own line
        - Each task must include:
          * Unique ID
          * Category (Design, Development, QA, DevOps)
          * Title
          * Description
          * Assigned Role
          * Estimated Hours
          * Priority (High, Medium, Low)
          * Acceptance Criteria (array of strings)
          * Optional Dependencies (array of task IDs)
        
        Example for each task:
        {{"id": "DESIGN-001", "category": "Design", "title": "Create UI/UX Wireframes", "description": "Develop initial wireframes for the application using Figma or similar tool. Include user flow diagrams and responsive design considerations.", "assignedRole": "UI/UX Designer", "estimatedHours": 16, "priority": "High", "acceptanceCriteria": ["Wireframes cover all main user flows", "Design is responsive for mobile and desktop", "Stakeholder approval obtained"], "dependencies": []}}
        
        Output each task as a complete JSON object on its own line so they can be streamed individually.
        Do not use a surrounding array. Output each task as a complete independent JSON object on its own line.`

        const prompt = ChatPromptTemplate.fromMessages([
            ['system', systemPrompt],
            [
                'human',
                'Project Feature: {feature}\nTechnology Stack: {techStack}\nAdditional Context: {context}\n\nPlease generate detailed tasks for this project.',
            ],
        ])

        // Store completed tasks
        const allTasks = []
        let buffer = ''

        // Process model output incrementally
        const messages = await prompt.formatMessages({
            feature: extractedInfo.feature,
            techStack: extractedInfo.techStack,
            context: summary,
        })

        // Initialize the LLM with streaming
        const model = new ChatOpenAI({
            modelName: 'gpt-4o-mini',
            temperature: 0.2,
            maxTokens: 2000,
            streaming: true,
            verbose: false,
        })

        try {
            const stream = await model.stream(messages)

            // Process the stream
            for await (const chunk of stream) {
                if (chunk.content) {
                    buffer += chunk.content

                    // Process any complete JSON objects in the buffer
                    let processedBuffer = ''

                    // Split by newlines and look for complete JSON objects
                    const lines = buffer.split('\n')

                    for (let i = 0; i < lines.length - 1; i++) {
                        const line = lines[i].trim()
                        if (line) {
                            try {
                                const taskObj = JSON.parse(line)

                                // Validate the task object has required fields
                                if (taskObj.id && taskObj.title && taskObj.description) {
                                    // Add to our collection of all tasks
                                    allTasks.push(taskObj)

                                    // Send this task to the client
                                    sendEvent(res, 'message', { task: taskObj })
                                }
                            } catch (e) {
                                // Not a complete JSON object, add to processed buffer
                                processedBuffer += lines[i] + '\n'
                            }
                        }
                    }

                    // Keep the last line in the buffer as it might be incomplete
                    buffer = processedBuffer + lines[lines.length - 1]
                }
            }

            // Process any remaining content in the buffer
            if (buffer.trim()) {
                // Try to find any valid JSON objects in remaining buffer
                const remainingLines = buffer.split('\n')
                for (const line of remainingLines) {
                    if (line.trim()) {
                        try {
                            const taskObj = JSON.parse(line.trim())
                            if (taskObj.id && taskObj.title && taskObj.description) {
                                allTasks.push(taskObj)
                                sendEvent(res, 'message', { task: taskObj })
                            }
                        } catch (e) {
                            // Ignore parsing errors in remaining buffer
                        }
                    }
                }
            }

            // Signal completion
            sendEvent(res, 'complete', { message: 'Stream completed' })
        } catch (streamError) {
            console.error('Stream error:', streamError)
            sendEvent(res, 'error', { error: 'An error occurred during streaming' })
        }

        res.end()
    } catch (error) {
        console.error('Error generating tasks:', error)
        sendEvent(res, 'error', { error: 'An error occurred while generating tasks. Please try again.' })
        res.end()
    }
}
