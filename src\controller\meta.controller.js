import { Op } from 'sequelize'
import Department from '../../db/models/department.js'
import Priority from '../../db/models/priority.js'
import Status from '../../db/models/status.js'
import User from '../../db/models/user.js'
import Workspace from '../../db/models/workspace.js'
import { AppError } from '../utils/app-error.js'
import { catchAsync } from '../utils/catch-async.js'
import { getPaginationParams, paginatedQuery } from '../utils/pagination.utils.js'
import Project from '../../db/models/project.js'
import Feature from '../../db/models/feature.js'
import TeamMemberRole from '../../db/models/team-member-role.js'
import Timezone from '../../db/models/timezones.js'
import UserRole from '../../db/models/user-role.js'

export const getDepartments = catchAsync(async (req, res, next) => {
    try {
        const departments = await Department.findAll({
            attributes: ['id', 'short_code', 'label'],
        })
        if (!departments) {
            return next(new AppError('Departments not found', 404))
        }
        return res.status(200).json({
            success: true,
            data: departments,
            message: 'Departments fetched successfully',
        })
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: 'An error occurred while fetching departments',
        })
    }
})

export const getStatuses = catchAsync(async (req, res) => {
    try {
        const statuses = await Status.findAll({
            order: [['id', 'ASC']],
            attributes: ['id', 'short_code', 'label', 'colour'],
        })
        if (!statuses) {
            return next(new AppError('Statuses not found', 404))
        }
        res.status(200).json({
            success: true,
            data: statuses,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching statuses',
            error: error.message,
        })
    }
})

export const getPriorities = catchAsync(async (req, res) => {
    try {
        const priorities = await Priority.findAll({
            order: [['id', 'ASC']],
            attributes: ['id', 'label', 'level'],
        })
        if (!priorities) {
            return next(new AppError('Priorities not found', 404))
        }
        res.status(200).json({
            success: true,
            data: priorities,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching priorities',
            error: error.message,
        })
    }
})

export const getProjects = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const { workspace_id, status_id, searchQuery } = req.query
    const paginationRequestData = getPaginationParams(req.query)
    // Build filter conditions
    const whereConditions = {}

    if (!workspace_id) {
        return next(new AppError('Workspace ID is required', 400))
    }
    whereConditions.workspace_id = workspace_id

    // Check if user has access to the workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    if (Array.isArray(status_id)) {
        whereConditions.status_id = {
            [Op.in]: status_id,
        }
    }

    if (searchQuery) {
        whereConditions[Op.or] = [
            {
                name: {
                    [Op.iLike]: `%${searchQuery}%`, // Case-insensitive search
                },
            },
        ]
    }

    // Get projects with associations and calculate progress
    const { data: projects, pagination } = await paginatedQuery(
        Project,
        {
            where: whereConditions,
            include: [
                {
                    model: Status,
                    attributes: ['id', 'short_code'],
                },
            ],
            attributes: ['id', 'name'],
            order: [['created_at', 'DESC']],
        },
        paginationRequestData
    )

    return res.status(200).json({
        status: 'success',
        data: projects,
    })
})

export const getFeatures = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const { workspace_id, project_id, searchQuery } = req.query
    const paginationRequestData = getPaginationParams(req.query)
    // Build filter conditions
    const whereConditions = {}

    if (!workspace_id) {
        return next(new AppError('Workspace ID is required', 400))
    }
    // whereConditions.workspace_id = workspace_id

    // Check if user has access to the workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    if (Array.isArray(project_id)) {
        whereConditions.project_id = {
            [Op.in]: project_id,
        }
    } else if (searchQuery) {
        whereConditions[Op.or] = [
            {
                title: {
                    [Op.iLike]: `%${searchQuery}%`, // Case-insensitive search
                },
            },
        ]
    }

    // Get projects with associations and calculate progress
    const { data: features, pagination } = await paginatedQuery(
        Feature,
        {
            where: whereConditions,
            attributes: ['id', 'feat_code', 'title'],
            include: [
                {
                    model: Project,
                    attributes: ['id', 'name'],
                    where: { workspace_id },
                },
            ],
            order: [['created_at', 'DESC']],
        },
        paginationRequestData
    )

    return res.status(200).json({
        status: 'success',
        data: features,
    })
})

export const getAssignees = catchAsync(async (req, res, next) => {
    const userId = req.user.id
    const { workspace_id, project_id, searchQuery } = req.query

    if (!workspace_id) {
        return next(new AppError('Workspace ID is required', 400))
    }

    // Check if user has access to the workspace
    const userWorkspace = await Workspace.findOne({
        where: { id: workspace_id },
        include: [
            {
                model: User,
                where: { id: userId },
                through: { attributes: [] },
            },
        ],
    })

    if (!userWorkspace) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Build where conditions for the search
    const whereConditions = {}

    if (searchQuery) {
        whereConditions[Op.or] = [
            { first_name: { [Op.iLike]: `%${searchQuery}%` } },
            { last_name: { [Op.iLike]: `%${searchQuery}%` } },
            { email: { [Op.iLike]: `%${searchQuery}%` } },
        ]
    }

    // Find users in the workspace
    const users = await User.findAll({
        where: whereConditions,
        attributes: ['id', 'first_name', 'last_name', 'email', 'img_url'],
        include: [
            {
                model: Workspace,
                where: { id: workspace_id },
                through: { attributes: [] },
                attributes: [],
            },
            // If project_id is provided, filter by project access
            // ...(project_id
            //     ? [
            //         {
            //             model: Project,
            //             where: { id: project_id },
            //             through: { attributes: [] },
            //             attributes: [],
            //         },
            //     ]
            //     : []),
        ],
    })

    return res.status(200).json({
        status: 'success',
        data: users,
    })
})

export const getRoles = catchAsync(async (req, res, next) => {
    // Get all roles
    const roles = await TeamMemberRole.findAll({
        attributes: ['id', 'role'],
    })

    return res.status(200).json({
        status: 'success',
        data: roles,
    })
})

export const getTimezones = catchAsync(async (req, res, next) => {
    // Get all timezones
    const timezones = await Timezone.findAll({
        attributes: ['id', 'display_name', 'gmt_offset'],
    })

    return res.status(200).json({
        status: 'success',
        data: timezones,
    })
})

export const getUserTypes = catchAsync(async (req, res, next) => {
    // Get all user types
    const userTypes = await UserRole.findAll({
        attributes: ['id', 'label', 'access_level'],
    })

    return res.status(200).json({
        status: 'success',
        data: userTypes,
    })
})
