'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date()

    await queryInterface.bulkInsert('task_user_mapping', [
      {
        user_id: 1, // <PERSON>
        task_id: 1, // Implement user login
        created_at: now,
        updated_at: now,
      },
      {
        user_id: 1, // <PERSON>
        task_id: 2, // Create login form UI
        created_at: now,
        updated_at: now,
      },
      {
        user_id: 2, // Albert
        task_id: 3, // Add login error handling
        created_at: now,
        updated_at: now,
      },
      {
        user_id: 2, // <PERSON>
        task_id: 4, // Design user profile page
        created_at: now,
        updated_at: now,
      },
      {
        user_id: 1, // <PERSON>
        task_id: 5, // Integrate profile form with backend
        created_at: now,
        updated_at: now,
      },
      {
        user_id: 2, // <PERSON>
        task_id: 6, // Add profile picture upload
        created_at: now,
        updated_at: now,
      },
    ])
  },

  async down(queryInterface, <PERSON>quelize) {
    await queryInterface.bulkDelete('task_user_mapping', null, {})
  },
}