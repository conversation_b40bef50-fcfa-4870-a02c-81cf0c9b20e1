import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Workspace = sequelize.define(
    'workspaces',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'name cannot be null',
                },
                notEmpty: {
                    msg: 'name cannot be empty',
                },
            },
        },
        invite_link: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        img_url: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        plan: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
    },
    {
        sequelize,
        modelName: 'Workspace',
        tableName: 'workspaces',
        timestamps: true,
        paranoid: true,
        underscored: true,
    },
)

const setupAssociations = async () => {
    try {
        const Project = (await import('./project.js')).default
        const User = (await import('./user.js')).default
        const UserWorkspaceMapping = (await import('./user-workspace-mapping.js')).default

        Workspace.hasMany(Project, { foreignKey: 'workspace_id' })
        Workspace.belongsToMany(User, {
            through: UserWorkspaceMapping,
            foreignKey: 'workspace_id',
            otherKey: 'user_id',
        })

        Workspace.hasMany(UserWorkspaceMapping, { foreignKey: 'workspace_id' })
        UserWorkspaceMapping.belongsTo(Workspace, { foreignKey: 'workspace_id' })
    } catch (error) {
        console.error('Error setting up Workspace associations:', error)
    }
}

setupAssociations()

export default Workspace
