import nodemailer from 'nodemailer'
import { AppError } from './app-error.js'

/**
 * Create a test transporter using Ethereal.email
 * This is useful for development and testing
 * @returns {Promise<Object>} Nodemailer transporter
 */
const createTestTransporter = async () => {
    try {
        console.log('Error : env not found using fallback test account.')
        // Create a test account at ethereal.email
        const testAccount = await nodemailer.createTestAccount()

        // Create a transporter using the test account
        const transporter = nodemailer.createTransport({
            host: testAccount.smtp.host,
            port: testAccount.smtp.port,
            secure: testAccount.smtp.secure,
            auth: {
                user: testAccount.user,
                pass: testAccount.pass,
            },
        })

        return transporter
    } catch (error) {
        throw new AppError('Failed to create test email transporter', 500)
    }
}

/**
 * Create a nodemailer transporter using SMTP
 * @returns {Promise<Object>} Nodemailer transporter
 */
const createTransporter = async () => {
    try {
        // Check if we have the required SMTP configuration
        if (!process.env.EMAIL_HOST || !process.env.EMAIL_PORT || !process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
            return await createTestTransporter()
        }

        const secure = process.env.EMAIL_SECURE === 'true'
        const port = parseInt(process.env.EMAIL_PORT, 10)

        const config = {
            host: process.env.EMAIL_HOST,
            port: port,
            secure: secure, // true for 465, false for other ports
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASSWORD,
            },
        }

        // If not using secure mode but using a common TLS port, add tls options
        if (!secure && (port === 587 || port === 25 || port === 2525)) {
            config.tls = {
                rejectUnauthorized: false, // Accepts self-signed certificates
                minVersion: 'TLSv1.2',
            }
        }

        return nodemailer.createTransport(config)
    } catch (error) {
        // Fall back to test email account on error
        return await createTestTransporter()
    }
}

/**
 * Send an email
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.text - Plain text content
 * @param {string} options.html - HTML content
 * @returns {Promise} - Resolves with info about the sent email
 */
export const sendEmail = async (options) => {
    try {
        const transporter = await createTransporter()

        const mailOptions = {
            from: `"${process.env.EMAIL_FROM_NAME || 'Fun2Plan'}" <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
            to: options.to,
            subject: options.subject,
            text: options.text || '',
            html: options.html || '',
        }

        const info = await transporter.sendMail(mailOptions)

        // Only log preview URL in development environment
        if (process.env.NODE_ENV !== 'production' && info && nodemailer.getTestMessageUrl) {
            const previewUrl = nodemailer.getTestMessageUrl(info)
            if (previewUrl) {
                console.log(`Email preview URL: ${previewUrl}`)
            }
        }

        return info
    } catch (error) {
        // Only log minimal error information
        throw new AppError('Failed to send email', 500)
    }
}

/**
 * Send a verification email with a code
 * @param {string} email - Recipient email
 * @param {string} verificationCode - The verification code
 * @returns {Promise} - Resolves with info about the sent email
 */
export const sendVerificationEmail = async (email, verificationCode) => {
    const subject = 'Verify Your Email Address'
    const text = `Your verification code is: ${verificationCode}. This code will expire in 15 minutes.`
    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <h2 style="color: #333;">Verify Your Email Address</h2>
            <p>Thank you for signing up! Please use the following code to verify your email address:</p>
            <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0; letter-spacing: 5px;">
                ${verificationCode}
            </div>
            <p>This code will expire in 15 minutes.</p>
            <p>If you didn't request this verification, you can safely ignore this email.</p>
            <p>Best regards,<br>The Fun2Plan Team</p>
        </div>
    `

    return sendEmail({ to: email, subject, text, html })
}

/**
 * Send a password reset email with a token
 * @param {string} email - Recipient email
 * @param {string} resetToken - The reset token
 * @returns {Promise} - Resolves with info about the sent email
 */
export const sendPasswordResetEmail = async (email, resetToken, resetUrl) => {
    console.log('email', email)
    const subject = 'Password Reset Request'
    const text = `Your password reset code is: ${resetToken}. This code will expire in 1 hour.`
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Request</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .email-container {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .email-header {
            background-color: #f5f5f5;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .email-header h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
        }
        .email-content {
            padding: 20px;
        }
        .reset-button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }
        .reset-button:hover {
            background-color: #2980b9;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            color: #888;
            padding: 10px;
            border-top: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h2>Password Reset Request</h2>
        </div>
        <div class="email-content">
            <p>Hello,</p>
            <p>You recently requested to reset your password for your Fun2Plan account. Click the button below to proceed:</p>
            
            <a href="${resetUrl}" class="reset-button">Reset Password</a>
            
            <p>Important details:</p>
            <ul>
                <li>This link will expire in 1 hour</li>
                <li>You'll need to provide both this link and your email address</li>
                <li>If you didn't request this reset, you can safely ignore this email</li>
            </ul>
            
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">${resetUrl}</p>
        </div>
        <div class="footer">
            © ${new Date().getFullYear()} Fun2Plan. All rights reserved.
        </div>
    </div>
</body>
</html>
`

    return sendEmail({ to: email, subject, text, html })
}
export const sendForgotPasswordEmail = async (email, resetToken) => {
    const subject = 'Password Reset Request'
    const text = `Your password reset code is: ${resetToken}. This code will expire in 1 hour.`
    const html = `
          <p>You requested a password reset.</p>
          <p>Click this link to reset your password:</p>
          <a href="${resetUrl}">Reset Password</a>
          <p>This link is valid for 1 hour.</p>
          <p>If you didn't request this, please ignore this email.</p>
        `
    return sendEmail({ to: email, subject, text, html })
}
