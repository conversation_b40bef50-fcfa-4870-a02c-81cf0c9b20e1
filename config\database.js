import pg from 'pg';
import config from '../config/config.js';

// Create the connection pool
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const pool = new pg.Pool({
  user: dbConfig.username,
  host: dbConfig.host,
  database: dbConfig.database,
  password: dbConfig.password,
  port: dbConfig.port,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection
});

// Add event handlers for the pool
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
});

export { pool };