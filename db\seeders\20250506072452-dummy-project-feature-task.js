'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        const now = new Date()

        await queryInterface.bulkInsert('tasks', [
            // Tasks for Feature 1
            {
                feat_id: 1,
                short_code: 'TASK001',
                title: 'Implement user login',
                description: 'Implement the backend logic for user login using JWT authentication.',
                time_estimate_hrs: 3,
                start_date: now,
                due_date: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000),
                status_id: 1,
                assignee: 1,
                created_by: 1,
                created_at: now,
                updated_at: now,
            },
            {
                feat_id: 1,
                short_code: 'TASK002',
                title: 'Create login form UI',
                description: 'Build the frontend login form and integrate with API.',
                time_estimate_hrs: 2,
                start_date: now,
                due_date: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000),
                status_id: 1,
                assignee: 1,
                created_by: 1,
                created_at: now,
                updated_at: now,
            },
            {
                feat_id: 1,
                short_code: 'TASK003',
                title: 'Add login error handling',
                description: 'Show error messages for failed logins and handle edge cases.',
                time_estimate_hrs: 1,
                start_date: now,
                due_date: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000),
                status_id: 2,
                assignee: 2,
                created_by: 1,
                created_at: now,
                updated_at: now,
            },

            // Tasks for Feature 2
            {
                feat_id: 2,
                short_code: 'TASK004',
                title: 'Design user profile page',
                description: 'Create the UI design for the user profile page with editable fields.',
                time_estimate_hrs: 5,
                start_date: now,
                due_date: new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000),
                status_id: 2,
                assignee: 2,
                created_by: 2,
                created_at: now,
                updated_at: now,
            },
            {
                feat_id: 2,
                short_code: 'TASK005',
                title: 'Integrate profile form with backend',
                description: 'Connect frontend profile page with backend update API.',
                time_estimate_hrs: 3,
                start_date: now,
                due_date: new Date(now.getTime() + 4 * 24 * 60 * 60 * 1000),
                status_id: 1,
                assignee: 1,
                created_by: 2,
                created_at: now,
                updated_at: now,
            },
            {
                feat_id: 2,
                short_code: 'TASK006',
                title: 'Add profile picture upload',
                description: 'Implement upload and preview of user profile images.',
                time_estimate_hrs: 2,
                start_date: now,
                due_date: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000),
                status_id: 1,
                assignee: 2,
                created_by: 2,
                created_at: now,
                updated_at: now,
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('tasks', null, {})
    },
}
