'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        const now = new Date()

        await queryInterface.bulkInsert(
            'statuses',
            [
                {
                    short_code: 'backlog',
                    label: 'Backlog',
                    colour: '#E0E0E0', // Light gray
                    kanban_column: true,
                    column_order: 1,
                    created_at: now,
                    updated_at: now,
                },
                {
                    short_code: 'in_progress',
                    label: 'In Progress',
                    colour: '#D97706', // Blue
                    kanban_column: true,
                    column_order: 2,
                    created_at: now,
                    updated_at: now,
                },
                {
                    short_code: 'review',
                    label: 'Review',
                    colour: '#E74C3C', // Red
                    kanban_column: true,
                    column_order: 3,
                    created_at: now,
                    updated_at: now,
                },
                {
                    short_code: 'delayed',
                    label: 'Delayed',
                    colour: '#F39C12', // Orange/Amber
                    created_at: now,
                    updated_at: now,
                },
                {
                    short_code: 'completed',
                    label: 'Completed',
                    colour: '#08B38B', // Green
                    kanban_column: true,
                    column_order: 4,
                    created_at: now,
                    updated_at: now,
                },
            ],
            {},
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('statuses', null, {})
    },
}
