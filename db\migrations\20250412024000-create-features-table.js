'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'features',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                feat_code: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                title: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                description: {
                    type: Sequelize.TEXT,
                    allowNull: true,
                },
                draft: {
                    type: Sequelize.BOOLEAN,
                    defaultValue: true,
                },
                project_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'projects',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                status_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'statuses',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                created_by: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                },
            },
            {
                underscored: true,
            },
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('features')
    },
}
