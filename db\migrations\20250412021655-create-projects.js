'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'projects',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                name: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                overview: {
                    type: Sequelize.TEXT,
                    allowNull: true,
                },
                workspace_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'workspaces',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                status_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                },
                stack: {
                    type: Sequelize.JSONB, // Store stack as array
                    allowNull: false,
                },
                due_date: {
                    type: Sequelize.DATE,
                },
                created_by: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
            },
            {
                underscored: true,
            },
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('projects')
    },
}
