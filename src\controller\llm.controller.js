import { searchSimilarItems } from '../llm/agents/vector.agent.js'
import { getAISummary } from '../llm/agents/summary.agent.js'
import { generateAITasks, continueConversation } from '../llm/agents/conversation.agent.js'
import { catchAsync } from '../utils/catch-async.js'
import { AppError } from '../utils/app-error.js'

// Function to extract entity type from natural language queries
const extractEntityType = (query) => {
    if (!query || typeof query !== 'string') {
        return null
    }
    
    const normalizedQuery = query.toLowerCase().trim()
    
    // Define keywords with weights for each entity type
    const entityKeywords = {
        task: {
            primary: ['task', 'tasks', 'todo', 'todos', 'assignment', 'assignments'],
            secondary: ['assigned to', 'responsible for', 'doing', 'complete', 'finish', 'deadline', 'due', 'deliverable']
        },
        project: {
            primary: ['project', 'projects', 'initiative', 'initiatives'],
            secondary: ['program', 'programs', 'campaign', 'campaigns']
        },
        feature: {
            primary: ['feature', 'features', 'functionality', 'functionalities'],
            secondary: ['component', 'components', 'module', 'modules', 'enhancement', 'enhancements', 'capability', 'capabilities']
        },
        workspace: {
            primary: ['workspace', 'workspaces', 'team', 'teams'],
            secondary: ['group', 'groups', 'department', 'departments', 'organization', 'organizations', 'space', 'spaces', 'environment', 'environments']
        }
    }
    
    // Context phrases that are less specific
    const contextPhrases = ['working on', 'involved in', 'part of', 'assigned to']
    
    // Score each entity type based on keyword matches with weights
    const scores = {}
    
    Object.keys(entityKeywords).forEach(entityType => {
        scores[entityType] = 0
        
        // Primary keywords get higher weight (3 points)
        entityKeywords[entityType].primary.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
            const matches = normalizedQuery.match(regex)
            if (matches) {
                scores[entityType] += matches.length * 3
            }
        })
        
        // Secondary keywords get medium weight (2 points)
        entityKeywords[entityType].secondary.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
            const matches = normalizedQuery.match(regex)
            if (matches) {
                scores[entityType] += matches.length * 2
            }
        })
        
        // Reduce score if only context phrases are found without primary entity keywords
        const hasContextOnly = contextPhrases.some(phrase => {
            const regex = new RegExp(`\\b${phrase}\\b`, 'gi')
            return normalizedQuery.match(regex)
        })
        
        const hasPrimaryKeyword = entityKeywords[entityType].primary.some(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
            return normalizedQuery.match(regex)
        })
        
        // If we only have context phrases without primary entity keywords, reduce score
        if (hasContextOnly && !hasPrimaryKeyword && scores[entityType] === 0) {
            // Don't add points for context-only matches
        }
    })
    
    // Find the entity type with the highest score
    const maxScore = Math.max(...Object.values(scores))
    
    // Return null if no keywords found
    if (maxScore === 0) {
        return null
    }
    
    // Return the entity type with the highest score
    const bestMatch = Object.keys(scores).find(key => scores[key] === maxScore)
    return bestMatch
}

/**
 * API endpoint to generate information based on search query
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<void>} - Streams response to client
 */
export const generateInfo = catchAsync(async (req, res, next) => {
    // Set appropriate headers for streaming
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')
    
    const { query, limit = 5, entityType = null } = req.body
    
    if (!query) {
        throw new AppError('Query is required', 400)
    }
    
    // Extract entity type from query if not explicitly provided
    const detectedEntityType = entityType || extractEntityType(query)
    
    // Create filter if entity type is detected or specified
    let filter = null
    if (detectedEntityType && ['workspace', 'project', 'feature', 'task'].includes(detectedEntityType)) {
        filter = {
            must: [
                {
                    key: 'type',
                    match: {
                        value: detectedEntityType,
                    },
                },
            ],
        }
    }
    
    // Search for similar items
    const results = await searchSimilarItems(query, limit, filter)
    
    // Generate and stream AI summary
    await getAISummary(results, query, res)
})


/**
 * API endpoint to handle project planning conversation and task generation
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<void>} - Streams response to client
 */
export const taskAIController = catchAsync(async (req, res, next) => {
    // Set appropriate headers for streaming
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')

    const { message, sessionId, generateTasks: shouldGenerateTasks } = req.body

    if (!message || !sessionId) {
        throw new AppError('Message and sessionId are required', 400)
    }

    if (shouldGenerateTasks) {
        await generateAITasks(sessionId, res)
    } else {
        await continueConversation(message, sessionId, res)
    }
})
