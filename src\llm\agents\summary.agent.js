import { ChatOpenAI } from '@langchain/openai'
import { ChatPromptTemplate } from '@langchain/core/prompts'
import { StringOutputParser } from '@langchain/core/output_parsers'

/**
 * Generate an AI summary for search results with streaming support using <PERSON><PERSON><PERSON><PERSON>
 * @param {Array} results - Search results to summarize
 * @param {string} query - Original search query
 * @param {Response} res - Express response object for streaming
 * @returns {Promise<void>} - Streams response directly to client
 */
async function getAISummary(results, query, res) {
    try {
        // Set appropriate headers for streaming
        res.setHeader('Content-Type', 'text/event-stream')
        res.setHeader('Cache-Control', 'no-cache')
        res.setHeader('Connection', 'keep-alive')

        // Format results for the AI prompt
        const formattedResults = results
            .map((result, index) => {
                const { payload, score } = result
                let formattedItem = `Result ${index + 1} (Score: ${score.toFixed(2)}):\n`
                formattedItem += `Type: ${payload.type}\n`

                // Add specific fields based on entity type
                switch (payload.type) {
                    case 'workspace':
                        formattedItem += `Name: ${payload.name}\n`
                        formattedItem += `Plan: ${payload.plan}\n`
                        break
                    case 'project':
                        formattedItem += `Name: ${payload.name}\n`
                        formattedItem += `Workspace: ${payload.workspace_name}\n`
                        formattedItem += `Status: ${payload.status}\n`
                        if (payload.overview) {
                            formattedItem += `Overview: ${payload.overview}\n`
                        }
                        break
                    case 'feature':
                        formattedItem += `Feature Code: ${payload.feat_code}\n`
                        formattedItem += `Title: ${payload.title}\n`
                        formattedItem += `Project: ${payload.project_name}\n`
                        formattedItem += `Status: ${payload.status}\n`
                        formattedItem += `Draft: ${payload.draft ? 'Yes' : 'No'}\n`
                        if (payload.description) {
                            formattedItem += `Description: ${payload.description}\n`
                        }
                        break
                    case 'task':
                        formattedItem += `Task Code: ${payload.short_code}\n`
                        formattedItem += `Title: ${payload.title}\n`
                        formattedItem += `Feature: ${payload.feature_code} - ${payload.feature_title}\n`
                        formattedItem += `Project: ${payload.project_name}\n`
                        formattedItem += `Status: ${payload.status}\n`
                        formattedItem += `Assigned to: ${payload.assignee}\n`
                        formattedItem += `Details : ${payload.text}\n`
                        if (payload.time_estimate_hrs) {
                            formattedItem += `Time estimate: ${payload.time_estimate_hrs} hours\n`
                        }
                        if (payload.start_date) {
                            formattedItem += `Start date: ${payload.start_date}\n`
                        }
                        if (payload.due_date) {
                            formattedItem += `Due date: ${payload.due_date}\n`
                        }
                        if (payload.description) {
                            formattedItem += `Description: ${payload.description}\n`
                        }
                        break
                }
                return formattedItem
            })
            .join('\n\n')

        // Create the system and human message templates
        const systemTemplate = `Answer the question based strictly on the provided data. Always include factual details such as status, assigned users, and other relevant entities if available. Be concise, clear, and friendly. Use professional but light emojis to keep the tone engaging (✨😊🔧📌✅). 

        When users ask about people (e.g., “Who is working on...”), always mention assigned users by name if present. If no one is assigned, state that clearly. Only elaborate when the user asks to. Do not infer or assume missing information.

        Keep the tone conversational but make sure the answer is informative and accurate.IMPORTANT: Talk like a literature student. Also provide a simple analysis of the data.`

        const humanTemplate = `Query: "{query}"

        Search Results:

        {results}

        Respond to the query based on the search results. add professional emojis from time to time.`
        // Please provide a concise summary with insights based on these results.`

        // Create the prompt template
        const chatPrompt = ChatPromptTemplate.fromMessages([
            ['system', systemTemplate],
            ['human', humanTemplate],
        ])

        // Initialize the LLM with streaming enabled
        const model = new ChatOpenAI({
            modelName: 'gpt-4o-mini',
            temperature: 0.3,
            maxTokens: 500,
            streaming: true,
        })

        // Create a simple pipeline with the prompt, model, and string output parser
        const chain = chatPrompt.pipe(model).pipe(new StringOutputParser())

        // Stream the response
        const stream = await chain.stream({
            query: query,
            results: formattedResults,
        })

        // Process each chunk from the stream and send it to the client
        for await (const chunk of stream) {
            res.write(chunk)
        }

        // End the response when streaming is complete
        res.end()
    } catch (error) {
        console.error('Error generating AI summary:', error)
        // Send error message if streaming fails
        res.write('Error generating summary. Please try again.')
        res.end()
        throw error
    }
}

export { getAISummary }
