import { Router } from 'express'
import { authentication } from '../controller/auth.controller.js'
import {
    createProject,
    getAllProjects,
    getProjectById,
    updateProject,
    deleteProject,
    addUserToProject,
    removeUserFromProject,
    saveProject,
    taskDistributionByDepartment
} from '../controller/project.controller.js'
import { validate } from '../middleware/validate.middleware.js'
import { createProjectSchema, updateProjectSchema, addUserToProjectSchema } from '../schemas/project.schema.js'
import { restrictToRoles } from '../middleware/rbac.middleware.js'
import { ROLES } from '../constants/roles.js'

const projectRouter = Router()

// Apply authentication middleware to all project routes
projectRouter.use(authentication)

// Routes for /api/v1/project
projectRouter.route('/').post(validate(createProjectSchema), createProject).get(getAllProjects)

// Routes for /api/v1/project/:id
projectRouter.route('/:id').get(getProjectById).put(validate(updateProjectSchema), updateProject).delete(deleteProject)

// Route for adding a user to a project
projectRouter
    .route('/user')
    .post(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), validate(addUserToProjectSchema), addUserToProject)

// Route for removing a user from a project
projectRouter
    .route('/:project_id/user/:user_id')
    .delete(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), removeUserFromProject)

projectRouter.route('/save').post(saveProject)
projectRouter.route('/task-distribution/department').get(taskDistributionByDepartment)


export default projectRouter
