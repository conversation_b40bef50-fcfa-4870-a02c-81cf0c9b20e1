import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Priority = sequelize.define(
    'priorities',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        label: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notNull: {
                    msg: 'label cannot be null',
                },
                notEmpty: {
                    msg: 'label cannot be empty',
                },
            },
        },
        level: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
        },
        color: {
            type: DataTypes.STRING(7),
            allowNull: true,
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            allowNull: false,
        },
    },
    {
        sequelize,
        modelName: 'Priority',
        tableName: 'priorities',
        timestamps: true,
        underscored: true,
    },
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        // const Project = (await import('./project.js')).default
        // const Feature = (await import('./feature.js')).default
        const Task = (await import('./task.js')).default

        // Priority.hasMany(Project, { foreignKey: 'priority_id' })
        // Priority.hasMany(Feature, { foreignKey: 'priority_id' })
        Priority.hasMany(Task, { foreignKey: 'priority_id', as: 'tasks' })
    } catch (error) {
        console.error('Error setting up Priority associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Priority
